<template>
  <div class="p-4">
    <h1 class="text-2xl font-bold mb-6">SimpleUpload 修复测试</h1>
    
    <div class="space-y-8">
      <!-- 测试 v-model:link 绑定 -->
      <div class="border p-4 rounded">
        <h2 class="text-lg font-semibold mb-4">1. 测试 v-model:link 绑定</h2>
        <SimpleUpload v-model:link="imageUrl" accept="image/*" />
        <div class="mt-2 text-sm text-gray-600">
          <p>当前图片链接：{{ imageUrl || '未设置' }}</p>
          <button 
            class="mt-2 px-3 py-1 bg-blue-500 text-white rounded text-sm"
            @click="imageUrl = 'https://example.com/test.jpg'"
          >
            手动设置链接测试
          </button>
        </div>
      </div>

      <!-- 测试 v-model:id-list 绑定 -->
      <div class="border p-4 rounded">
        <h2 class="text-lg font-semibold mb-4">2. 测试 v-model:id-list 绑定</h2>
        <SimpleUpload 
          v-model:id-list="fileIdList" 
          :max-count="3" 
          :show-upload-list="true" 
        />
        <div class="mt-2 text-sm text-gray-600">
          <p>文件ID列表：{{ fileIdList.length ? fileIdList.join(', ') : '无文件' }}</p>
          <button 
            class="mt-2 px-3 py-1 bg-green-500 text-white rounded text-sm"
            @click="fileIdList = [1, 2, 3]"
          >
            手动设置ID列表测试
          </button>
        </div>
      </div>

      <!-- 测试 v-model 绑定 -->
      <div class="border p-4 rounded">
        <h2 class="text-lg font-semibold mb-4">3. 测试 v-model 绑定</h2>
        <SimpleUpload v-model="fileId" />
        <div class="mt-2 text-sm text-gray-600">
          <p>文件ID：{{ fileId || '未设置' }}</p>
          <button 
            class="mt-2 px-3 py-1 bg-purple-500 text-white rounded text-sm"
            @click="fileId = 999"
          >
            手动设置文件ID测试
          </button>
        </div>
      </div>

      <!-- 测试其他属性传递 -->
      <div class="border p-4 rounded">
        <h2 class="text-lg font-semibold mb-4">4. 测试其他属性传递</h2>
        <SimpleUpload 
          v-model:link="docUrl"
          list-type="text"
          btn-text="上传文档"
          accept=".pdf,.doc,.docx"
        />
        <div class="mt-2 text-sm text-gray-600">
          <p>文档链接：{{ docUrl || '未设置' }}</p>
          <p class="text-xs text-gray-500">应该显示为文本按钮，按钮文字为"上传文档"</p>
        </div>
      </div>

      <!-- 测试自定义 API -->
      <div class="border p-4 rounded">
        <h2 class="text-lg font-semibold mb-4">5. 测试自定义 API 覆盖</h2>
        <SimpleUpload 
          v-model:link="customUrl"
          :upload-api="customUploadApi"
          :preview-api="customPreviewApi"
        />
        <div class="mt-2 text-sm text-gray-600">
          <p>自定义链接：{{ customUrl || '未设置' }}</p>
          <p class="text-xs text-gray-500">使用自定义 API，应该在控制台看到自定义日志</p>
        </div>
      </div>
    </div>

    <!-- 调试信息 -->
    <div class="mt-8 p-4 bg-gray-100 rounded">
      <h3 class="text-lg font-semibold mb-2">调试信息</h3>
      <pre class="text-sm">{{ debugInfo }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

// 根据当前应用导入 SimpleUpload
// 在 web-it 应用中使用：
// import { SimpleUpload } from '#/components';
// 在 web-supply-chain 应用中使用：
// import { SimpleUpload } from '#/components';

// 测试数据
const imageUrl = ref('');
const fileIdList = ref<number[]>([]);
const fileId = ref<number>();
const docUrl = ref('');
const customUrl = ref('');

// 自定义 API 函数
const customUploadApi = async (data: any, config: any) => {
  console.log('🔥 使用自定义上传 API', data, config);
  // 模拟上传
  return {
    url: 'https://custom-upload-result.jpg',
    attachId: Math.floor(Math.random() * 1000),
    link: 'https://custom-upload-result.jpg'
  };
};

const customPreviewApi = async (params: { id: string | number }) => {
  console.log('🔍 使用自定义预览 API', params);
  // 模拟预览
  return `https://custom-preview-${params.id}.jpg`;
};

// 调试信息
const debugInfo = computed(() => ({
  imageUrl: imageUrl.value,
  fileIdList: fileIdList.value,
  fileId: fileId.value,
  docUrl: docUrl.value,
  customUrl: customUrl.value,
}));
</script>

<style scoped>
.space-y-8 > * + * {
  margin-top: 2rem;
}
</style>
