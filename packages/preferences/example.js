// 深度合并示例
import { defineOverridesPreferences } from './src/index.ts';

// 示例 1: 部分覆盖配置
const example1 = defineOverridesPreferences({
  header: {
    height: 80, // 覆盖默认的 60
    enable: true, // 新增属性
  },
  theme: {
    mode: 'dark', // 覆盖默认的 'light'
    // radius 保持默认值 '0.25'
  },
});

console.log('示例 1 - 部分覆盖:', JSON.stringify(example1, null, 2));

// 示例 2: 空配置，使用所有默认值
const example2 = defineOverridesPreferences({});

console.log('示例 2 - 使用默认值:', JSON.stringify(example2, null, 2));

// 示例 3: 添加新的配置项
const example3 = defineOverridesPreferences({
  app: {
    name: 'My Custom App',
    version: '1.0.0',
  },
  sidebar: {
    collapsedButton: true, // 覆盖默认的 false
    // fixedButton 保持默认值 false
    width: 250, // 新增属性
  },
});

console.log('示例 3 - 添加新配置:', JSON.stringify(example3, null, 2));
