import { downloadAndSaveWithAutoFileName } from '@vben/shared';

import { requestClient } from '../request-client';

/**
 * 使用增强下载功能的示例
 * 这些示例展示了如何使用新的下载功能，优先从 Content-Disposition 响应头解析文件名
 */

/**
 * 示例1: 使用 requestClient 的新方法下载文件并自动解析文件名
 */
export async function downloadWithAutoFileName(url: string, fallbackFileName?: string) {
  try {
    const result = await requestClient.downloadWithFileName(url, {
      fallbackFileName,
    });

    console.log('Downloaded file:', result.fileName);
    console.log('File size:', result.blob.size, 'bytes');

    return result;
  } catch (error) {
    console.error('Download failed:', error);
    throw error;
  }
}

/**
 * 示例2: 使用 requestClient 直接下载并保存文件
 */
export async function downloadAndSave(url: string, fallbackFileName?: string) {
  try {
    await requestClient.downloadAndSave(url, {
      fallbackFileName,
    });

    console.log('File downloaded and saved successfully');
  } catch (error) {
    console.error('Download and save failed:', error);
    throw error;
  }
}

/**
 * 示例3: 使用通用工具函数下载文件
 */
export async function downloadUsingUtilFunction(url: string, fallbackFileName?: string) {
  try {
    await downloadAndSaveWithAutoFileName({
      url,
      fallbackFileName,
      headers: {
        Authorization: 'Bearer your-token-here', // 如果需要认证
      },
    });

    console.log('File downloaded using util function');
  } catch (error) {
    console.error('Download using util function failed:', error);
    throw error;
  }
}

/**
 * 示例4: 获取文件信息而不立即下载
 */
export async function getFileInfo(url: string) {
  try {
    const result = await requestClient.downloadWithFileName(url);

    return {
      fileName: result.fileName,
      fileSize: result.blob.size,
      fileType: result.blob.type,
    };
  } catch (error) {
    console.error('Failed to get file info:', error);
    throw error;
  }
}

/**
 * 示例5: 批量下载文件
 */
export async function downloadMultipleFiles(urls: string[]) {
  const results = [];

  for (const url of urls) {
    try {
      const result = await requestClient.downloadWithFileName(url);
      results.push({
        url,
        fileName: result.fileName,
        success: true,
        blob: result.blob,
      });
    } catch (error) {
      results.push({
        url,
        fileName: null,
        success: false,
        error: error.message,
      });
    }
  }

  return results;
}

/**
 * 示例6: 下载文件并显示进度（需要配合其他进度监控机制）
 */
export async function downloadWithProgress(url: string, onProgress?: (loaded: number, total: number) => void) {
  try {
    // 注意：这里的进度监控需要在请求配置中添加 onDownloadProgress
    const result = await requestClient.downloadWithFileName(url, {
      config: {
        onDownloadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            onProgress(progressEvent.loaded, progressEvent.total);
          }
        },
      },
    });

    return result;
  } catch (error) {
    console.error('Download with progress failed:', error);
    throw error;
  }
}

/**
 * 示例7: 改进的云盘下载函数
 * 这个示例展示了如何改进现有的云盘下载功能
 */
export async function improvedCloudDiskDownload(fileIds: string[]) {
  try {
    // 假设这是获取下载链接的 API 调用
    const response = await requestClient.post('/cloud-disk/pack-download', fileIds);

    // 使用新的下载功能，优先从响应头解析文件名
    await requestClient.downloadAndSave(response.url, {
      fallbackFileName: response.name, // 使用 API 返回的名称作为备用
    });

    console.log('Cloud disk file downloaded successfully');
  } catch (error) {
    console.error('Cloud disk download failed:', error);
    throw error;
  }
}
