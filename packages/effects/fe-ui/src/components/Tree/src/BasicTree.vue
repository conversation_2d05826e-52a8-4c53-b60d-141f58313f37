<script lang="tsx">
import type { CSSProperties } from 'vue';

import type { Recordable } from '@vben/types';

import type { CheckKeys, FieldNames, KeyType, TreeActionType, TreeItem, TreeState } from './types/tree';

import type { CreateContextOptions } from '#/components/ContextMenu';

import { computed, defineComponent, onMounted, reactive, ref, toRaw, unref, watch, watchEffect } from 'vue';

// import { TreeIcon } from './TreeIcon';
import { VbenIcon } from '@vben-core/shadcn-ui';

import { Spin, Tree } from 'ant-design-vue';
import { cloneDeep, difference, get, omit } from 'lodash-es';

import { ScrollContainer } from '#/components/Container';
import { FeEmpty } from '#/components/Fe/Empty';
import { useContextMenu } from '#/hooks/web/useContextMenu';
import { createBEM } from '#/utils/bem';
import { eachTree, filter, treeToList } from '#/utils/helper/treeHelper';
import { extendSlots, getSlot } from '#/utils/helper/tsxHelper';
import { isArray, isBoolean, isFunction } from '#/utils/is';

import TreeHeader from './components/TreeHeader.vue';
import { useTree } from './hooks/useTree';
import { treeEmits, treeProps } from './types/tree';

export default defineComponent({
  name: 'BasicTree',
  inheritAttrs: false,
  props: treeProps,
  emits: treeEmits,
  setup(props, { attrs, slots, emit, expose }) {
    const [bem] = createBEM('tree');

    const state = reactive<TreeState>({
      checkStrictly: props.checkStrictly,
      expandedKeys: props.expandedKeys || [],
      selectedKeys: props.selectedKeys || [],
      checkedKeys: props.checkedKeys || [],
      halfCheckedKeys: [],
      checkedNodes: [],
    });

    const searchState = reactive({
      startSearch: false,
      searchText: '',
      searchData: [] as TreeItem[],
    });

    const treeDataRef = ref<TreeItem[]>([]);

    const [createContextMenu] = useContextMenu();

    const getFieldNames = computed((): Required<FieldNames> => {
      const { fieldNames } = props;
      return {
        children: 'children',
        title: 'name',
        key: 'id',
        ...fieldNames,
      };
    });

    const getBindValues = computed(() => {
      const propsData = {
        blockNode: true,
        ...attrs,
        ...props,
        expandedKeys: state.expandedKeys,
        selectedKeys: state.selectedKeys,
        checkedKeys: state.checkedKeys,
        checkStrictly: state.checkStrictly,
        fieldNames: { ...unref(getFieldNames), title: `${unref(getFieldNames).title}_title` },
        'onUpdate:expandedKeys': (v: KeyType[]) => {
          state.expandedKeys = v;
          emit('update:expandedKeys', v);
        },
        'onUpdate:selectedKeys': (v: KeyType[]) => {
          state.selectedKeys = v;
          emit('update:selectedKeys', v);
        },
        onCheck: (v: CheckKeys, e) => {
          let currentValue = toRaw(state.checkedKeys) as KeyType[];
          if (isArray(currentValue) && searchState.startSearch) {
            const value = e.node.eventKey;
            currentValue = difference(currentValue, getChildrenKeys(value));
            if (e.checked) {
              currentValue.push(value);
            }
            state.checkedKeys = currentValue;
          } else {
            state.checkedKeys = v;
          }
          state.halfCheckedKeys = e.halfCheckedKeys;
          state.checkedNodes = e.checkedNodes;
          const rawVal = toRaw(state.checkedKeys);
          emit('update:value', rawVal);
          emit('check', rawVal, e);
        },
        onSelect: (_selectedKeys, nodeInfo) => {
          const { key } = nodeInfo.node;
          if (!props.checkable) state.selectedKeys = [key];
          emit('select', [key], nodeInfo);
        },
        onRightClick: handleRightClick,
      };
      return omit(propsData, 'treeData', 'class');
    });
    const getTreeData = computed((): TreeItem[] =>
      searchState.startSearch ? searchState.searchData : unref(treeDataRef),
    );

    const getNotFound = computed((): boolean => {
      return !getTreeData.value || getTreeData.value.length === 0;
    });

    const {
      deleteNodeByKey,
      insertNodeByKey,
      insertNodesByKey,
      filterByLevel,
      updateNodeByKey,
      getAllKeys,
      getChildrenKeys,
      getEnabledKeys,
      getParentKeys,
      getSelectedNode,
      getEnabledNodes,
    } = useTree(treeDataRef, getFieldNames);

    function getIcon(params: TreeItem, icon?: string) {
      if (!icon && props.renderIcon && isFunction(props.renderIcon)) {
        return props.renderIcon(params);
      }
      return icon;
    }

    async function handleRightClick({ event, node }: Recordable) {
      const { rightMenuList: menuList = [], beforeRightClick } = props;
      const contextMenuOptions: CreateContextOptions = { event, items: [] };

      if (beforeRightClick && isFunction(beforeRightClick)) {
        const result = await beforeRightClick(node, event);
        if (Array.isArray(result)) {
          contextMenuOptions.items = result;
        } else {
          Object.assign(contextMenuOptions, result);
        }
      } else {
        contextMenuOptions.items = menuList;
      }
      if (!contextMenuOptions.items?.length) return;
      contextMenuOptions.items = contextMenuOptions.items.filter((item) => !item.hidden);
      createContextMenu(contextMenuOptions);
    }

    function setExpandedKeys(keys: KeyType[]) {
      state.expandedKeys = keys;
    }

    function getExpandedKeys() {
      return state.expandedKeys;
    }
    function setSelectedKeys(keys: KeyType[]) {
      state.selectedKeys = keys;
    }

    function getSelectedKeys() {
      return state.selectedKeys;
    }

    function setCheckedKeys(keys: CheckKeys) {
      state.checkedKeys = keys;
    }

    function getCheckedKeys() {
      return state.checkedKeys;
    }
    function getHalfCheckedKeys() {
      return state.halfCheckedKeys;
    }

    function getCheckedNodes() {
      return state.checkedNodes;
    }

    function checkAll(checkAll: boolean) {
      state.checkedKeys = checkAll ? getEnabledKeys() : ([] as KeyType[]);
      state.checkedNodes = checkAll ? getEnabledNodes(state.checkedKeys) : [];
    }

    function expandAll(expandAll: boolean) {
      state.expandedKeys = expandAll ? getAllKeys() : ([] as KeyType[]);
    }

    function onStrictlyChange(strictly: boolean) {
      state.checkStrictly = strictly;
    }

    watch(
      () => props.searchValue,
      (val) => {
        if (val !== searchState.searchText) {
          searchState.searchText = val;
        }
      },
      {
        immediate: true,
      },
    );

    watch(
      () => props.treeData,
      (val) => {
        if (val) {
          handleSearch(searchState.searchText);
        }
      },
    );

    function handleSearch(searchValue: string) {
      if (searchValue !== searchState.searchText) searchState.searchText = searchValue;
      emit('update:searchValue', searchValue);
      if (!searchValue) {
        searchState.startSearch = false;
        return;
      }
      const { filterFn, checkable, expandOnSearch, checkOnSearch, selectedOnSearch } = unref(props);
      searchState.startSearch = true;
      const { title: titleField, key: keyField } = unref(getFieldNames);

      const matchedKeys: string[] = [];
      searchState.searchData = filter(
        unref(treeDataRef),
        (node) => {
          const result = filterFn
            ? filterFn(searchValue, node, unref(getFieldNames))
            : (node[titleField]?.includes(searchValue) ?? false);
          if (result) {
            matchedKeys.push(node[keyField]);
          }
          return result;
        },
        unref(getFieldNames),
      );

      if (expandOnSearch) {
        const expandKeys = treeToList(searchState.searchData).map((val) => {
          return val[keyField];
        });
        if (expandKeys && expandKeys.length > 0) {
          setExpandedKeys(expandKeys);
        }
      }

      if (checkOnSearch && checkable && matchedKeys.length > 0) {
        setCheckedKeys(matchedKeys);
      }

      if (selectedOnSearch && matchedKeys.length > 0) {
        setSelectedKeys(matchedKeys);
      }
    }

    function handleClickNode(key: string, children: TreeItem[]) {
      if (!props.clickRowToExpand || (!attrs['load-data'] && (!children || children.length === 0))) return;
      if (state.expandedKeys.includes(key)) {
        const keys = [...state.expandedKeys];
        const index = keys.indexOf(key);
        if (index !== -1) {
          keys.splice(index, 1);
        }
        setExpandedKeys(keys);
      } else {
        setExpandedKeys([...state.expandedKeys, key]);
      }
    }

    watchEffect(() => {
      treeDataRef.value = props.treeData as TreeItem[];
      if (props.defaultExpandAll) expandAll(true);
    });

    onMounted(() => {
      const level = Number.parseInt(props.defaultExpandLevel);
      if (level > 0) {
        state.expandedKeys = filterByLevel(level);
      } else if (props.defaultExpandAll) {
        expandAll(true);
      }
    });

    watchEffect(() => {
      state.expandedKeys = props.expandedKeys;
    });

    watchEffect(() => {
      state.selectedKeys = props.selectedKeys;
    });

    watchEffect(() => {
      state.checkedKeys = props.checkedKeys;
    });

    watch(
      () => props.value,
      () => {
        state.checkedKeys = toRaw(props.value || []);
      },
      { immediate: true },
    );

    watch(
      () => state.checkedKeys,
      () => {
        const v = toRaw(state.checkedKeys);
        emit('update:value', v);
        emit('change', v);
      },
    );

    watchEffect(() => {
      state.checkStrictly = props.checkStrictly;
    });

    const instance: TreeActionType = {
      setExpandedKeys,
      getExpandedKeys,
      setSelectedKeys,
      getSelectedKeys,
      setCheckedKeys,
      getCheckedKeys,
      getHalfCheckedKeys,
      getParentKeys,
      insertNodeByKey,
      insertNodesByKey,
      deleteNodeByKey,
      updateNodeByKey,
      getSelectedNode,
      getCheckedNodes,
      getAllKeys,
      checkAll,
      expandAll,
      filterByLevel: (level: number) => {
        state.expandedKeys = filterByLevel(level);
      },
      setSearchValue: (value: string) => {
        handleSearch(value);
      },
      getSearchValue: () => {
        return searchState.searchText;
      },
    };

    function renderAction(node: TreeItem) {
      const { actionList } = props;
      if (!actionList || actionList.length === 0) return;
      return actionList.map((item, index) => {
        let nodeShow = true;
        if (isFunction(item.show)) {
          nodeShow = item.show?.(node);
        } else if (isBoolean(item.show)) {
          nodeShow = item.show;
        }

        if (!nodeShow) return null;

        return (
          <span class={bem('action')} key={index}>
            {item.render(node)}
          </span>
        );
      });
    }

    const treeData = computed(() => {
      const data = cloneDeep(getTreeData.value);
      eachTree(data, unref(getFieldNames), (item, _parent) => {
        const searchText = searchState.searchText;
        const { highlight } = unref(props);
        const { title: titleField, key: keyField, children: childrenField } = unref(getFieldNames);

        const icon = getIcon(item, item.icon);
        const title = get(item, titleField);

        const searchIdx = searchText ? title.indexOf(searchText) : -1;
        // const isHighlight = searchState.startSearch && !isEmpty(searchText) && highlight && searchIdx !== -1;
        // const highlightStyle = `color: ${isBoolean(highlight) ? '#f50' : highlight}`;

        // const titleDom = isHighlight ? (
        //   <span class={unref(getBindValues)?.blockNode ? `${bem('content')}` : ''}>
        //     <span>{title.substr(0, searchIdx)}</span>
        //     <span style={highlightStyle}>{searchText}</span>
        //     <span>{title.substr(searchIdx + (searchText as string).length)}</span>
        //   </span>
        // ) : (
        //   title
        // );
        const titleDom = title;
        item[`${titleField}_title`] = (
          <span
            class={`${bem('title')}`}
            onClick={handleClickNode.bind(null, item[keyField], item[childrenField])}
            title={title}
          >
            {slots?.title ? (
              getSlot(slots, 'title', item)
            ) : (
              <>
                {icon && <VbenIcon class="mr-1" icon={icon} />}
                <span class={bem('name')}>{titleDom}</span>
                <span class={bem('actions')}>{renderAction(item)}</span>
              </>
            )}
          </span>
        );
        return item;
      });
      return data;
    });

    expose(instance);

    return () => {
      const { title, helpMessage, toolbar, search, checkable } = props;
      const showTitle = title || toolbar || search || slots.headerTitle;
      const scrollStyle: CSSProperties = { height: '100%' };
      return (
        <div class={[bem(), 'h-full', attrs.class]}>
          {showTitle && (
            <TreeHeader
              checkable={checkable}
              checkAll={checkAll}
              expandAll={expandAll}
              helpMessage={helpMessage}
              onSearch={handleSearch}
              onStrictlyChange={onStrictlyChange}
              search={search}
              searchText={searchState.searchText}
              title={title}
              toolbar={toolbar}
            >
              {extendSlots(slots)}
            </TreeHeader>
          )}
          <Spin spinning={unref(props.loading)} tip="加载中..." wrapperClassName={unref(props.treeWrapperClassName)}>
            <ScrollContainer style={scrollStyle} v-show={!unref(getNotFound)}>
              <Tree {...unref(getBindValues)} showIcon={false} treeData={treeData.value} />
            </ScrollContainer>
            <FeEmpty v-show={unref(getNotFound)} />
          </Spin>
        </div>
      );
    };
  },
});
</script>
<style lang="less">
@import '#/style/index.less';

@tree-prefix-cls: ~'fe-tree';

.@{tree-prefix-cls} {
  background-color: @component-background;

  &.remove-active-tree {
    .ant-tree .ant-tree-treenode.ant-tree-treenode-selected {
      background-color: @selected-hover-bg;
    }
  }

  .ant-tree {
    .ant-tree-treenode {
      padding: 0;

      &:hover {
        background-color: @selected-hover-bg;
      }

      &.ant-tree-treenode-selected {
        background-color: @tree-node-selected-bg;
      }
    }

    .ant-tree-switcher {
      line-height: 32px;

      .ant-tree-switcher-icon {
        vertical-align: 0.25em;
      }
    }

    .ant-tree-checkbox {
      margin: 8px 8px 0 0;
      margin-block-start: 0 !important;
    }

    .ant-tree-node-content-wrapper {
      position: relative;
      height: 32px;
      line-height: 32px;

      &:hover,
      &.ant-tree-node-selected {
        background-color: unset !important;
      }

      .ant-tree-title {
        position: absolute;
        left: 0;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  &__title {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    padding-right: 10px;

    &:hover {
      .@{tree-prefix-cls} __action {
        visibility: visible;
      }
    }
  }

  &__name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__content {
    overflow: hidden;
  }

  &__actions {
    position: absolute;
    //top: 2px;
    right: 3px;
    display: flex;
  }

  &__action {
    visibility: hidden;
    margin-left: 4px;
  }

  &-header {
    border-bottom: 1px solid @border-color-base;
  }

  .ant-empty-normal {
    padding: 32px 0;
    margin: 0 !important;
  }
}
</style>
