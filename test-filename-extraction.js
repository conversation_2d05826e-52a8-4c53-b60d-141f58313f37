// 测试文件名提取逻辑
function extractFileName(url, id) {
  let fileName = `file-${id}`;
  
  try {
    const urlPath = new URL(url).pathname;
    const extractedName = urlPath.split('/').pop();
    if (extractedName && extractedName.includes('.')) {
      fileName = decodeURIComponent(extractedName);
    }
  } catch (urlError) {
    // 如果 URL 解析失败，尝试简单的字符串分割
    const parts = url.split('/');
    const lastPart = parts[parts.length - 1];
    if (lastPart && lastPart.includes('.')) {
      fileName = decodeURIComponent(lastPart);
    }
  }
  
  return fileName;
}

// 测试用例
const testCases = [
  {
    url: 'http://192.168.3.100:9000/file-dev/upload/20250731/7238a20da2f4be10af3ef830a2d393e1.xlsx',
    id: 21,
    expected: '7238a20da2f4be10af3ef830a2d393e1.xlsx'
  },
  {
    url: 'http://192.168.3.100:9000/file-dev/upload/20250731/document.pdf',
    id: 22,
    expected: 'document.pdf'
  },
  {
    url: 'http://192.168.3.100:9000/file-dev/upload/20250731/%E5%95%86%E5%93%81%E6%B8%85%E5%8D%95.xlsx',
    id: 23,
    expected: '商品清单.xlsx'
  },
  {
    url: 'http://192.168.3.100:9000/file-dev/upload/20250731/image.png',
    id: 24,
    expected: 'image.png'
  },
  {
    url: 'invalid-url-without-extension',
    id: 25,
    expected: 'file-25'
  },
  {
    url: 'http://192.168.3.100:9000/file-dev/upload/20250731/no-extension',
    id: 26,
    expected: 'file-26'
  }
];

console.log('文件名提取测试结果：');
console.log('='.repeat(50));

testCases.forEach((testCase, index) => {
  const result = extractFileName(testCase.url, testCase.id);
  const passed = result === testCase.expected;
  
  console.log(`测试 ${index + 1}: ${passed ? '✅ 通过' : '❌ 失败'}`);
  console.log(`  URL: ${testCase.url}`);
  console.log(`  期望: ${testCase.expected}`);
  console.log(`  实际: ${result}`);
  console.log('');
});
