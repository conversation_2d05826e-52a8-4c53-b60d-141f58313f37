<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { ThirdPartyLogInfo } from '#/api';

import { Page } from '@vben/common-ui';
import { defineFormOptions } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getThirdPartyLogPageListApi } from '#/api';

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'serviceName',
      label: '三方服务名称',
    },
    {
      component: 'Input',
      fieldName: 'userName',
      label: '调用人',
    },
    {
      component: 'SearchRangePicker',
      fieldName: 'callTime',
      label: '调用时间',
    },
  ],
  fieldMappingTime: [['callTime', ['callStartTime', 'callEndTime'], 'x']],
});
const gridOptions: VxeTableGridOptions<ThirdPartyLogInfo> = {
  columns: [
    { field: 'callTime', title: '调用时间' },
    { field: 'serviceName', title: '三方服务名称' },
    { field: 'callCount', title: '调用次数' },
    { field: 'status', title: '调用状态', formatter: ['formatBoolean', { true: '成功', false: '失败' }] },
    { field: 'userName', title: '调用人' },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getThirdPartyLogPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
};
const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
</script>

<template>
  <Page auto-content-height>
    <Grid />
  </Page>
</template>

<style></style>
