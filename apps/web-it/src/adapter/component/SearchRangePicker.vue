<script setup lang="ts">
import type { TimeRangePickerProps } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';

import { computed, useAttrs } from 'vue';

import { RangePicker } from 'ant-design-vue';
import dayjs from 'dayjs';

// 定义 Emits，确保类型安全
interface SearchRangePickerEmits {
  (e: 'update:value', value: any): void;
  (e: 'change', dates: [Dayjs, Dayjs] | null, dateStrings: any): void;
}

// 继承 Ant Design Vue RangePicker 的所有 props
// 这样父组件就可以传递任何 RangePicker 支持的 prop
const props = defineProps<TimeRangePickerProps>();
const emit = defineEmits<SearchRangePickerEmits>();
const attrs = useAttrs();

// 将 props 和 attrs (非 prop 的 attribute 如 style, class) 合并
// 这样可以确保所有 attribute 都能正确传递给底层的 a-range-picker
const componentAttrs = computed(() => ({ ...props, ...attrs }));

// 计算属性，用于处理 v-model 的输入
// 它将父组件传入的各种格式的 value (时间戳、字符串等) 统一转换为 dayjs 对象数组
const internalValue = computed((): [Dayjs, Dayjs] | null => {
  const { value } = props;
  if (!value || !Array.isArray(value) || value.length !== 2 || !value[0] || !value[1]) {
    return null;
  }
  if (dayjs.isDayjs(value[0])) {
    return value as [Dayjs, Dayjs];
  }
  if (typeof value[0] === 'string' || typeof value[0] === 'number') {
    const parser = (val: number | string) =>
      props.valueFormat === 'x' ? dayjs(Number(val)) : dayjs(val as string, props.valueFormat as string);
    return [parser(value[0]), parser(value[1])];
  }
  return null;
});

// @change 事件的核心处理逻辑
const handleDateChange = (
  dates: [Dayjs, Dayjs] | null,
  // dateStrings 参数类型与 antdv 保持一致
  dateStrings: [null, null] | [string, string],
) => {
  if (dates && dates.length === 2) {
    const startDate = dates[0];
    const endDate = dates[1].endOf('day'); // 核心逻辑：设置结束时间

    let finalValues: any = [startDate, endDate];
    let finalDateStrings: [string, string] | null = [startDate.format(), endDate.format()];

    if (props.valueFormat) {
      const formatter = (date: Dayjs) =>
        props.valueFormat === 'x' ? date.format('x') : date.format(props.valueFormat as string);

      finalValues = [formatter(startDate), formatter(endDate)];
      finalDateStrings = finalValues;
    }

    emit('update:value', finalValues);
    emit('change', [startDate, endDate], finalDateStrings);
  } else {
    // 处理清空操作
    emit('update:value', null);
    emit('change', null, null);
  }
};
</script>

<template>
  <RangePicker :value="internalValue" v-bind="componentAttrs" @change="handleDateChange" />
</template>
